import cv2
import numpy as np
import os
import argparse

def crop_center_image(input_path, output_path, crop_width, crop_height):
    """
    从图像中心截取指定宽高的区域并保存
    
    参数:
    input_path: 输入图像路径
    output_path: 输出图像路径
    crop_width: 截取区域的宽度
    crop_height: 截取区域的高度
    
    返回:
    bool: 操作是否成功
    """
    try:
        # 读取图像
        img = cv2.imread(input_path)
        if img is None:
            print(f"错误: 无法读取图像 {input_path}")
            return False
        
        # 获取原图像尺寸
        original_height, original_width = img.shape[:2]
        print(f"原图像尺寸: {original_width} x {original_height}")
        
        # 检查截取尺寸是否超过原图像
        if crop_width > original_width or crop_height > original_height:
            print(f"警告: 截取尺寸 ({crop_width} x {crop_height}) 超过原图像尺寸 ({original_width} x {original_height})")
            # 调整截取尺寸为原图像尺寸
            crop_width = min(crop_width, original_width)
            crop_height = min(crop_height, original_height)
            print(f"调整后的截取尺寸: {crop_width} x {crop_height}")
        
        # 计算中心点
        center_x = original_width // 2
        center_y = original_height // 2
        
        # 计算截取区域的起始和结束坐标
        start_x = center_x - crop_width // 2
        end_x = start_x + crop_width
        start_y = center_y - crop_height // 2
        end_y = start_y + crop_height
        
        # 确保坐标在有效范围内
        start_x = max(0, start_x)
        start_y = max(0, start_y)
        end_x = min(original_width, end_x)
        end_y = min(original_height, end_y)
        
        print(f"截取区域: ({start_x}, {start_y}) 到 ({end_x}, {end_y})")
        
        # 截取图像
        cropped_img = img[start_y:end_y, start_x:end_x]
        
        # 保存截取的图像
        success = cv2.imwrite(output_path, cropped_img)
        if success:
            print(f"成功保存截取的图像到: {output_path}")
            print(f"截取后图像尺寸: {cropped_img.shape[1]} x {cropped_img.shape[0]}")
            return True
        else:
            print(f"错误: 保存图像失败")
            return False
            
    except Exception as e:
        print(f"处理图像时发生错误: {e}")
        return False

def main():
    """
    主函数 - 支持命令行参数
    """
    parser = argparse.ArgumentParser(description='从图像中心截取指定尺寸的区域')
    parser.add_argument('-i', '--input', default='test.bmp', 
                       help='输入图像文件路径 (默认: test.bmp)')
    parser.add_argument('-o', '--output', default='cropped_center.bmp',
                       help='输出图像文件路径 (默认: cropped_center.bmp)')
    parser.add_argument('-w', '--width', type=int, default=800,
                       help='截取区域的宽度 (默认: 800)')
    parser.add_argument('--height', type=int, default=600,
                       help='截取区域的高度 (默认: 600)')
    
    args = parser.parse_args()
    
    # 检查输入文件是否存在
    if not os.path.exists(args.input):
        print(f"错误: 输入文件 {args.input} 不存在")
        return
    
    # 验证宽高参数
    if args.width <= 0 or args.height <= 0:
        print("错误: 宽度和高度必须大于0")
        return
    
    print(f"开始处理图像...")
    print(f"输入文件: {args.input}")
    print(f"输出文件: {args.output}")
    print(f"截取尺寸: {args.width} x {args.height}")
    print("-" * 50)
    
    # 执行截取操作
    success = crop_center_image(args.input, args.output, args.width, args.height)
    
    if success:
        print("-" * 50)
        print("图像处理完成!")
    else:
        print("-" * 50)
        print("图像处理失败!")

if __name__ == "__main__":
    main()
